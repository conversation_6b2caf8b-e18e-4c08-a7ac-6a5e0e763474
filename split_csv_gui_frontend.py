import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
from split_csv_gui import split_file, merge_files, select_file, select_files, select_directory
import threading
import sys
from io import StringIO

class CSVSplitterGUI:
    def __init__(self):
        self.window = tk.Tk()
        self.window.title("CSV/Excel Splitter and Merger")
        self.window.geometry("600x400")
        self.window.resizable(True, True)
        
        # Create notebook for tabs
        self.notebook = ttk.Notebook(self.window)
        self.notebook.pack(expand=True, fill='both', padx=10, pady=5)
        
        # Create Split and Merge tabs
        self.split_tab = ttk.Frame(self.notebook)
        self.merge_tab = ttk.Frame(self.notebook)
        self.notebook.add(self.split_tab, text='Split File')
        self.notebook.add(self.merge_tab, text='Merge Files')
        
        self.setup_split_tab()
        self.setup_merge_tab()

    def setup_split_tab(self):
        # File selection
        ttk.Label(self.split_tab, text="Input File:").pack(pady=5)
        self.input_file_var = tk.StringVar()
        ttk.Entry(self.split_tab, textvariable=self.input_file_var, width=50).pack(pady=5)
        ttk.Button(self.split_tab, text="Browse", command=self.browse_input_file).pack(pady=5)

        # Output directory selection
        ttk.Label(self.split_tab, text="Output Directory:").pack(pady=5)
        self.output_dir_var = tk.StringVar()
        ttk.Entry(self.split_tab, textvariable=self.output_dir_var, width=50).pack(pady=5)
        ttk.Button(self.split_tab, text="Browse", command=self.browse_output_dir).pack(pady=5)

        # Chunk size input
        ttk.Label(self.split_tab, text="Chunk Size (rows):").pack(pady=5)
        self.chunk_size_var = tk.StringVar(value="1000")
        ttk.Entry(self.split_tab, textvariable=self.chunk_size_var, width=20).pack(pady=5)

        # Split button
        ttk.Button(self.split_tab, text="Split File", command=self.split_action).pack(pady=20)

    def setup_merge_tab(self):
        # File selection
        ttk.Label(self.merge_tab, text="Input Files:").pack(pady=5)
        self.files_listbox = tk.Listbox(self.merge_tab, width=50, height=8)
        self.files_listbox.pack(pady=5)
        ttk.Button(self.merge_tab, text="Select Files", command=self.browse_merge_files).pack(pady=5)

        # Merge button
        ttk.Button(self.merge_tab, text="Merge Files", command=self.merge_action).pack(pady=20)

    def browse_input_file(self):
        file_path = select_file()
        if file_path:
            self.input_file_var.set(file_path)

    def browse_output_dir(self):
        dir_path = select_directory()
        if dir_path:
            self.output_dir_var.set(dir_path)

    def browse_merge_files(self):
        files = select_files()
        if files:
            self.files_listbox.delete(0, tk.END)
            for file in files:
                self.files_listbox.insert(tk.END, file)

    def split_action(self):
        input_file = self.input_file_var.get()
        output_dir = self.output_dir_var.get()
        chunk_size = self.chunk_size_var.get()

        if not input_file or not output_dir or not chunk_size:
            messagebox.showerror("Error", "Please fill in all fields")
            return

        try:
            chunk_size = int(chunk_size)
            if chunk_size <= 0:
                raise ValueError("Chunk size must be positive")
        except ValueError:
            messagebox.showerror("Error", "Please enter a valid positive number for chunk size")
            return

        # Create a progress window
        progress_window = tk.Toplevel(self.window)
        progress_window.title("Splitting File...")
        progress_window.geometry("400x200")
        progress_window.transient(self.window)
        progress_window.grab_set()

        # Progress label
        progress_label = ttk.Label(progress_window, text="Processing file...")
        progress_label.pack(pady=10)

        # Progress bar
        progress_bar = ttk.Progressbar(progress_window, mode='indeterminate')
        progress_bar.pack(pady=10, padx=20, fill='x')
        progress_bar.start()

        # Text area for detailed messages
        text_area = scrolledtext.ScrolledText(progress_window, height=6, width=50)
        text_area.pack(pady=10, padx=10, fill='both', expand=True)

        def update_progress(message):
            text_area.insert(tk.END, message + "\n")
            text_area.see(tk.END)
            progress_window.update()

        try:
            # Capture print statements from split_file function
            old_stdout = sys.stdout
            sys.stdout = captured_output = StringIO()

            update_progress("Starting file split operation...")
            split_file(input_file, output_dir, chunk_size)

            # Get captured output
            output = captured_output.getvalue()
            sys.stdout = old_stdout

            # Show any messages from the split operation
            if output:
                for line in output.strip().split('\n'):
                    if line.strip():
                        update_progress(line)

            progress_bar.stop()
            progress_window.destroy()
            messagebox.showinfo("Success", "File split successfully!")

        except Exception as e:
            sys.stdout = old_stdout
            progress_bar.stop()
            progress_window.destroy()

            error_msg = str(e)
            if "Error tokenizing data" in error_msg:
                detailed_msg = (
                    f"CSV parsing error detected:\n\n{error_msg}\n\n"
                    "This usually means your CSV file has formatting issues like:\n"
                    "• Unescaped commas in text fields\n"
                    "• Missing quotes around fields\n"
                    "• Inconsistent number of columns\n\n"
                    "The updated splitter should handle this automatically. "
                    "If it still fails, try cleaning your CSV file first."
                )
                messagebox.showerror("CSV Format Error", detailed_msg)
            else:
                messagebox.showerror("Error", f"Error splitting file: {error_msg}")

    def merge_action(self):
        files = list(self.files_listbox.get(0, tk.END))
        if not files:
            messagebox.showerror("Error", "Please select files to merge")
            return

        output_file = select_file()
        if not output_file:
            return

        try:
            merge_files(files, output_file)
            messagebox.showinfo("Success", "Files merged successfully!")
        except Exception as e:
            messagebox.showerror("Error", f"Error merging files: {str(e)}")

    def run(self):
        self.window.mainloop()

if __name__ == "__main__":
    app = CSVSplitterGUI()
    app.run()