#!/usr/bin/env python3
"""
Debug script to test the split function directly
"""

from split_csv_gui import split_file
import os

def test_split():
    print("🔍 Testing split function directly...")
    
    # You'll need to update these paths to match your actual file
    input_file = input("Enter path to your CSV file: ").strip().strip('"')
    output_dir = input("Enter output directory: ").strip().strip('"')
    chunk_size = int(input("Enter chunk size: ").strip())
    
    print(f"\n📋 Test parameters:")
    print(f"   Input file: {input_file}")
    print(f"   Output dir: {output_dir}")
    print(f"   Chunk size: {chunk_size}")
    print(f"   File exists: {os.path.exists(input_file)}")
    print(f"   Output dir exists: {os.path.exists(output_dir)}")
    
    print(f"\n🚀 Starting split operation...")
    print("=" * 50)
    
    try:
        split_file(input_file, output_dir, chunk_size)
        print("=" * 50)
        print("✅ Split function completed!")
        
        # Check if files were created
        if os.path.exists(output_dir):
            files = [f for f in os.listdir(output_dir) if f.endswith('.csv')]
            print(f"📁 Files in output directory: {len(files)}")
            for f in files[:5]:  # Show first 5
                print(f"   - {f}")
            if len(files) > 5:
                print(f"   ... and {len(files) - 5} more")
        else:
            print("❌ Output directory doesn't exist!")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_split()
