#!/usr/bin/env python3
"""
Split or merge CSV / Excel files via a tiny Tk UI.

• Split mode  – choose one file → pick an output folder → enter chunk size.
• Merge mode – pick one or many files → choose where to save combined file.

Dependencies:
    pip install pandas openpyxl
"""

import os
import tkinter as tk
from tkinter import filedialog
import pandas as pd


# ───────────────────────── File/Folder pickers ──────────────────────────
def select_files() -> list[str]:
    """
    Ask for multiple CSV / XLSX files.

    Returns a real list on every platform (macOS sometimes returns a single
    space-separated string, which we split with `root.tk.splitlist`).
    """
    root = tk.Tk()
    root.withdraw()

    paths = filedialog.askopenfilenames(
        parent=root,
        title="Select the files to merge",
        filetypes=[
            ("Excel & CSV files", ("*.xlsx", "*.csv")),
            ("Excel files", "*.xlsx"),
            ("CSV files", "*.csv"),
        ],
    )

    # macOS quirk: askopen<PERSON>lena<PERSON> may return one big string
    if isinstance(paths, str):
        paths = root.tk.splitlist(paths)

    root.destroy()
    return list(paths)          # always a list (possibly empty)


def select_file() -> str:
    """Single CSV / XLSX file."""
    root = tk.Tk()
    root.withdraw()
    path = filedialog.askopenfilename(
        parent=root,
        title="Select a file",
        filetypes=[
            ("Excel & CSV files", ("*.xlsx", "*.csv")),
            ("Excel files", "*.xlsx"),
            ("CSV files", "*.csv"),
        ],
    )
    root.destroy()
    return path


def select_directory() -> str:
    """Folder picker."""
    root = tk.Tk()
    root.withdraw()
    folder = filedialog.askdirectory(parent=root, title="Choose output folder")
    root.destroy()
    return folder


# ───────────────────────── Core functionality ───────────────────────────
def split_file(input_file: str, output_dir: str, chunk_size: int) -> None:
    """Split a CSV or XLSX into fixed-row chunks."""
    try:
        ext = os.path.splitext(input_file)[1].lower()
        if ext == ".csv":
            # Try multiple strategies to read problematic CSV files while preserving column structure
            df = None
            parsing_method = "unknown"

            try:
                # First attempt: standard reading
                df = pd.read_csv(input_file, dtype=str)
                parsing_method = "standard pandas"
                print("✓ Successfully parsed using standard pandas CSV reader")

            except pd.errors.ParserError as e:
                print(f"Standard CSV parsing failed: {e}")
                print("🔍 Scanning file for ALL problematic lines...")

                # Comprehensive bad line detection
                bad_lines = []
                expected_columns = None

                try:
                    # Get expected column count from header
                    with open(input_file, 'r', encoding='utf-8') as f:
                        first_line = f.readline().strip()
                        expected_columns = len(first_line.split(','))
                    print(f"Expected columns based on header: {expected_columns}")

                    # Scan entire file for problematic lines
                    with open(input_file, 'r', encoding='utf-8') as f:
                        for line_num, line in enumerate(f, 1):
                            if line_num == 1:  # Skip header
                                continue

                            # Count columns in this line (simple comma count)
                            actual_columns = len(line.strip().split(','))
                            if actual_columns != expected_columns:
                                bad_lines.append({
                                    'line_num': line_num,
                                    'expected': expected_columns,
                                    'found': actual_columns,
                                    'content': line.strip()[:100] + '...' if len(line.strip()) > 100 else line.strip()
                                })

                            # Limit scan to avoid overwhelming output
                            if len(bad_lines) >= 20:
                                print(f"⚠ Found 20+ bad lines, stopping scan...")
                                break

                    if bad_lines:
                        print(f"🚨 Found {len(bad_lines)} problematic lines:")
                        for i, bad_line in enumerate(bad_lines[:10]):  # Show first 10
                            print(f"   Line {bad_line['line_num']}: {bad_line['expected']} → {bad_line['found']} columns")
                            print(f"      Content: {bad_line['content']}")

                        if len(bad_lines) > 10:
                            print(f"   ... and {len(bad_lines) - 10} more problematic lines")

                        print(f"\n🔧 QUICK FIX SUGGESTIONS:")
                        print(f"   1. AUTOMATIC FIX: Use 'Skip bad lines' mode (recommended)")
                        print(f"   2. MANUAL FIX: Open CSV in text editor and check lines: {[bl['line_num'] for bl in bad_lines[:5]]}")
                        print(f"   3. EXCEL FIX: Open in Excel, save as CSV again")
                        print(f"   4. COMMON ISSUES:")
                        print(f"      • Unescaped commas in text fields")
                        print(f"      • Missing quotes around fields containing commas")
                        print(f"      • Extra commas at end of lines")
                        print(f"      • Line breaks within quoted fields")

                except Exception as scan_error:
                    print(f"Could not scan file: {scan_error}")

                print("Trying alternative parsing methods...")

                try:
                    # Second attempt: use python engine (more forgiving but preserves structure)
                    df = pd.read_csv(input_file, dtype=str, engine='python')
                    parsing_method = "python engine"
                    print("✓ Successfully parsed using Python engine")

                except Exception as e2:
                    print(f"Python engine failed: {e2}")

                    try:
                        # Third attempt: skip bad lines but SAVE them to separate file
                        # First, try to read just the header to get column names
                        header_df = pd.read_csv(input_file, dtype=str, nrows=1)
                        expected_columns = list(header_df.columns)
                        print(f"Expected columns from header: {expected_columns}")

                        # Save bad lines to separate error file
                        if bad_lines:
                            error_file_path = os.path.join(
                                output_dir,
                                f"{os.path.splitext(os.path.basename(input_file))[0]}_ERRORS.csv"
                            )

                            print(f"💾 Saving {len(bad_lines)} problematic lines to: {error_file_path}")

                            # Create error file with bad line data
                            error_data = []
                            for bad_line in bad_lines:
                                # Try to parse the bad line as best as possible
                                line_parts = bad_line['content'].split(',')

                                # Pad or truncate to match expected columns
                                if len(line_parts) < expected_columns:
                                    # Add empty fields for missing columns
                                    line_parts.extend([''] * (expected_columns - len(line_parts)))
                                elif len(line_parts) > expected_columns:
                                    # Combine extra fields into the last column
                                    extra_data = ','.join(line_parts[expected_columns-1:])
                                    line_parts = line_parts[:expected_columns-1] + [extra_data]

                                # Add metadata about the error
                                error_row = {
                                    'ORIGINAL_LINE_NUMBER': bad_line['line_num'],
                                    'ERROR_TYPE': f"Expected {bad_line['expected']} columns, found {bad_line['found']}",
                                    'ORIGINAL_CONTENT': bad_line['content']
                                }

                                # Add the parsed data with column names
                                for i, col_name in enumerate(expected_columns):
                                    error_row[col_name] = line_parts[i] if i < len(line_parts) else ''

                                error_data.append(error_row)

                            # Save error data to CSV
                            error_df = pd.DataFrame(error_data)
                            error_df.to_csv(error_file_path, index=False)
                            print(f"✅ Error file saved with {len(error_data)} problematic rows")

                        # Now read with bad line skipping
                        df = pd.read_csv(input_file, dtype=str, on_bad_lines='skip')
                        parsing_method = "skip bad lines"
                        print("✓ Successfully parsed by skipping bad lines")

                        if bad_lines:
                            print(f"📊 RESULT: Skipped {len(bad_lines)} problematic lines")
                            print(f"   Clean data: {len(df)} rows")
                            print(f"   Bad data saved to: {os.path.basename(error_file_path)}")

                        # Verify column structure is preserved
                        if list(df.columns) != expected_columns:
                            print("⚠ Warning: Column structure may have changed")
                            print(f"Original: {expected_columns}")
                            print(f"Current:  {list(df.columns)}")

                    except Exception as e3:
                        print(f"Skip bad lines failed: {e3}")
                        print("❌ All CSV parsing methods failed!")
                        return

            if df is None:
                print("❌ Could not parse CSV file with any method")
                return

            print(f"Final parsing method used: {parsing_method}")

        elif ext == ".xlsx":
            df = pd.read_excel(input_file, dtype=str)
        else:
            print(f"Unsupported file type: {ext}")
            return

        # Debug information
        print(f"Input file: {input_file}")
        print(f"Output directory: {output_dir}")
        print(f"File extension: {ext}")
        print(f"DataFrame shape: {df.shape}")
        print(f"DataFrame columns: {list(df.columns)}")

        if df.empty:
            print("ERROR: DataFrame is empty! No data to split.")
            return

        # Column integrity check
        print(f"Column count: {len(df.columns)}")
        print(f"Sample of first row data: {df.iloc[0].tolist()[:5] if len(df) > 0 else 'No data'}")

        # Check for unnamed columns (often indicates parsing issues)
        unnamed_cols = [col for col in df.columns if 'Unnamed:' in str(col)]
        if unnamed_cols:
            print(f"⚠ Warning: Found {len(unnamed_cols)} unnamed columns: {unnamed_cols}")
            print("This might indicate column misalignment during parsing.")

        os.makedirs(output_dir, exist_ok=True)
        print(f"Created/verified output directory: {output_dir}")

        total_rows = len(df)
        num_chunks = -(-total_rows // chunk_size)  # ceiling division

        print(f"Total rows: {total_rows}")
        print(f"Chunk size: {chunk_size}")
        print(f"Number of chunks to create: {num_chunks}")

        for i in range(num_chunks):
            start_row = i * chunk_size
            end_row = (i + 1) * chunk_size
            chunk = df.iloc[start_row:end_row]

            out_name = os.path.join(
                output_dir,
                f"{os.path.splitext(os.path.basename(input_file))[0]}_{i+1:06}{ext}",
            )

            print(f"Creating chunk {i + 1}/{num_chunks}: rows {start_row}-{end_row-1} -> {out_name}")
            print(f"Chunk shape: {chunk.shape}")

            try:
                if ext == ".csv":
                    chunk.to_csv(out_name, index=False)
                else:
                    chunk.to_excel(out_name, index=False, engine="openpyxl")

                # Verify file was created
                if os.path.exists(out_name):
                    file_size = os.path.getsize(out_name)
                    print(f"✓ Chunk {i + 1} saved successfully: {out_name} ({file_size} bytes)")
                else:
                    print(f"❌ ERROR: File was not created: {out_name}")

            except Exception as chunk_error:
                print(f"❌ ERROR saving chunk {i + 1}: {chunk_error}")

    except Exception as e:
        print(f"Error during splitting: {e}")


def merge_files(input_files: list[str], output_file: str) -> None:
    """Concatenate many CSV / XLSX files into one."""
    try:
        if not input_files:
            print("No files selected for merging.")
            return

        ext = os.path.splitext(input_files[0])[1].lower()
        if ext not in {".csv", ".xlsx"}:
            print("Unsupported file format. Please select CSV or Excel only.")
            return

        frames = []
        for file in input_files:
            if file.endswith(".csv"):
                df = pd.read_csv(file, dtype=str)
            elif file.endswith(".xlsx"):
                df = pd.read_excel(file, dtype=str)
            else:
                print(f"Skipping unsupported file: {file}")
                continue
            frames.append(df)

        merged_df = pd.concat(frames, ignore_index=True)

        if ext == ".csv":
            merged_df.to_csv(output_file, index=False)
        else:
            merged_df.to_excel(output_file, index=False, engine="openpyxl")

        print(f"Files successfully merged into {output_file}")

    except Exception as e:
        print(f"Error during merging: {e}")


# ───────────────────────────── CLI wrapper ───────────────────────────────
def main() -> None:
    print("Welcome! Please select an operation:")
    print("1. Split a file")
    print("2. Merge files")
    choice = input("Enter your choice (1 or 2): ").strip()

    if choice == "1":  # ── Split ─────────────────────────────────────────
        input_file = select_file()
        if not input_file:
            print("No file selected. Exiting.")
            return

        output_dir = select_directory()
        if not output_dir:
            print("No directory selected. Exiting.")
            return

        chunk_size_str = input("Rows per chunk: ").strip()
        if not chunk_size_str.isdigit():
            print("Invalid chunk size. Exiting.")
            return

        split_file(input_file, output_dir, int(chunk_size_str))

    elif choice == "2":  # ── Merge ───────────────────────────────────────
        input_files = select_files()
        if not input_files:
            print("No files selected. Exiting.")
            return

        root = tk.Tk()
        root.withdraw()
        output_file = filedialog.asksaveasfilename(
            parent=root,
            title="Save merged file as…",
            defaultextension=".csv",
            filetypes=[
                ("Excel & CSV files", ("*.xlsx", "*.csv")),
                ("Excel files", "*.xlsx"),
                ("CSV files", "*.csv"),
            ],
        )
        root.destroy()

        if not output_file:
            print("No output file selected. Exiting.")
            return

        merge_files(input_files, output_file)

    else:
        print("Invalid choice. Exiting.")


if __name__ == "__main__":
    main()
