#!/usr/bin/env python3
"""
Split or merge CSV / Excel files via a tiny Tk UI.

• Split mode  – choose one file → pick an output folder → enter chunk size.
• Merge mode – pick one or many files → choose where to save combined file.

Dependencies:
    pip install pandas openpyxl
"""

import os
import tkinter as tk
from tkinter import filedialog
import pandas as pd


# ───────────────────────── File/Folder pickers ──────────────────────────
def select_files() -> list[str]:
    """
    Ask for multiple CSV / XLSX files.

    Returns a real list on every platform (macOS sometimes returns a single
    space-separated string, which we split with `root.tk.splitlist`).
    """
    root = tk.Tk()
    root.withdraw()

    paths = filedialog.askopenfilenames(
        parent=root,
        title="Select the files to merge",
        filetypes=[
            ("Excel & CSV files", ("*.xlsx", "*.csv")),
            ("Excel files", "*.xlsx"),
            ("CSV files", "*.csv"),
        ],
    )

    # macOS quirk: askopen<PERSON>lena<PERSON> may return one big string
    if isinstance(paths, str):
        paths = root.tk.splitlist(paths)

    root.destroy()
    return list(paths)          # always a list (possibly empty)


def select_file() -> str:
    """Single CSV / XLSX file."""
    root = tk.Tk()
    root.withdraw()
    path = filedialog.askopenfilename(
        parent=root,
        title="Select a file",
        filetypes=[
            ("Excel & CSV files", ("*.xlsx", "*.csv")),
            ("Excel files", "*.xlsx"),
            ("CSV files", "*.csv"),
        ],
    )
    root.destroy()
    return path


def select_directory() -> str:
    """Folder picker."""
    root = tk.Tk()
    root.withdraw()
    folder = filedialog.askdirectory(parent=root, title="Choose output folder")
    root.destroy()
    return folder


# ───────────────────────── Core functionality ───────────────────────────
def split_file(input_file: str, output_dir: str, chunk_size: int) -> None:
    """Split a CSV or XLSX into fixed-row chunks."""
    try:
        ext = os.path.splitext(input_file)[1].lower()
        if ext == ".csv":
            df = pd.read_csv(input_file, dtype=str)
        elif ext == ".xlsx":
            df = pd.read_excel(input_file, dtype=str)
        else:
            print(f"Unsupported file type: {ext}")
            return

        # Debug information
        print(f"Input file: {input_file}")
        print(f"Output directory: {output_dir}")
        print(f"File extension: {ext}")
        print(f"DataFrame shape: {df.shape}")
        print(f"DataFrame columns: {list(df.columns)}")

        if df.empty:
            print("ERROR: DataFrame is empty! No data to split.")
            return

        os.makedirs(output_dir, exist_ok=True)
        print(f"Created/verified output directory: {output_dir}")

        total_rows = len(df)
        num_chunks = -(-total_rows // chunk_size)  # ceiling division

        print(f"Total rows: {total_rows}")
        print(f"Chunk size: {chunk_size}")
        print(f"Number of chunks to create: {num_chunks}")

        for i in range(num_chunks):
            start_row = i * chunk_size
            end_row = (i + 1) * chunk_size
            chunk = df.iloc[start_row:end_row]

            out_name = os.path.join(
                output_dir,
                f"{os.path.splitext(os.path.basename(input_file))[0]}_{i+1:06}{ext}",
            )

            print(f"Creating chunk {i + 1}/{num_chunks}: rows {start_row}-{end_row-1} -> {out_name}")
            print(f"Chunk shape: {chunk.shape}")

            try:
                if ext == ".csv":
                    chunk.to_csv(out_name, index=False)
                else:
                    chunk.to_excel(out_name, index=False, engine="openpyxl")

                # Verify file was created
                if os.path.exists(out_name):
                    file_size = os.path.getsize(out_name)
                    print(f"✓ Chunk {i + 1} saved successfully: {out_name} ({file_size} bytes)")
                else:
                    print(f"❌ ERROR: File was not created: {out_name}")

            except Exception as chunk_error:
                print(f"❌ ERROR saving chunk {i + 1}: {chunk_error}")

    except Exception as e:
        print(f"Error during splitting: {e}")


def merge_files(input_files: list[str], output_file: str) -> None:
    """Concatenate many CSV / XLSX files into one."""
    try:
        if not input_files:
            print("No files selected for merging.")
            return

        ext = os.path.splitext(input_files[0])[1].lower()
        if ext not in {".csv", ".xlsx"}:
            print("Unsupported file format. Please select CSV or Excel only.")
            return

        frames = []
        for file in input_files:
            if file.endswith(".csv"):
                df = pd.read_csv(file, dtype=str)
            elif file.endswith(".xlsx"):
                df = pd.read_excel(file, dtype=str)
            else:
                print(f"Skipping unsupported file: {file}")
                continue
            frames.append(df)

        merged_df = pd.concat(frames, ignore_index=True)

        if ext == ".csv":
            merged_df.to_csv(output_file, index=False)
        else:
            merged_df.to_excel(output_file, index=False, engine="openpyxl")

        print(f"Files successfully merged into {output_file}")

    except Exception as e:
        print(f"Error during merging: {e}")


# ───────────────────────────── CLI wrapper ───────────────────────────────
def main() -> None:
    print("Welcome! Please select an operation:")
    print("1. Split a file")
    print("2. Merge files")
    choice = input("Enter your choice (1 or 2): ").strip()

    if choice == "1":  # ── Split ─────────────────────────────────────────
        input_file = select_file()
        if not input_file:
            print("No file selected. Exiting.")
            return

        output_dir = select_directory()
        if not output_dir:
            print("No directory selected. Exiting.")
            return

        chunk_size_str = input("Rows per chunk: ").strip()
        if not chunk_size_str.isdigit():
            print("Invalid chunk size. Exiting.")
            return

        split_file(input_file, output_dir, int(chunk_size_str))

    elif choice == "2":  # ── Merge ───────────────────────────────────────
        input_files = select_files()
        if not input_files:
            print("No files selected. Exiting.")
            return

        root = tk.Tk()
        root.withdraw()
        output_file = filedialog.asksaveasfilename(
            parent=root,
            title="Save merged file as…",
            defaultextension=".csv",
            filetypes=[
                ("Excel & CSV files", ("*.xlsx", "*.csv")),
                ("Excel files", "*.xlsx"),
                ("CSV files", "*.csv"),
            ],
        )
        root.destroy()

        if not output_file:
            print("No output file selected. Exiting.")
            return

        merge_files(input_files, output_file)

    else:
        print("Invalid choice. Exiting.")


if __name__ == "__main__":
    main()
