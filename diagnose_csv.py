#!/usr/bin/env python3
"""
Diagnose CSV file issues - find problematic lines and suggest fixes.
"""

import csv
import sys
from tkinter import filedialog
import tkinter as tk

def diagnose_csv_file():
    """Analyze a CSV file for common issues."""
    
    # Select the problematic CSV file
    root = tk.Tk()
    root.withdraw()
    
    csv_file = filedialog.askopenfilename(
        parent=root,
        title="Select the problematic CSV file",
        filetypes=[("CSV files", "*.csv"), ("All files", "*.*")]
    )
    root.destroy()
    
    if not csv_file:
        print("No file selected.")
        return
    
    print(f"Analyzing: {csv_file}")
    print("=" * 50)
    
    # First, let's check the file encoding
    encodings_to_try = ['utf-8', 'utf-8-sig', 'latin1', 'cp1252', 'iso-8859-1']
    
    for encoding in encodings_to_try:
        try:
            with open(csv_file, 'r', encoding=encoding) as f:
                first_line = f.readline()
            print(f"✓ File can be read with encoding: {encoding}")
            working_encoding = encoding
            break
        except UnicodeDecodeError:
            continue
    else:
        print("❌ Could not determine file encoding")
        return
    
    # Analyze the CSV structure
    issues_found = []
    line_count = 0
    expected_columns = None
    problematic_lines = []
    
    try:
        with open(csv_file, 'r', encoding=working_encoding, newline='') as f:
            # Try to detect delimiter
            sample = f.read(1024)
            f.seek(0)
            
            sniffer = csv.Sniffer()
            try:
                dialect = sniffer.sniff(sample)
                delimiter = dialect.delimiter
                print(f"✓ Detected delimiter: '{delimiter}'")
            except:
                delimiter = ','
                print(f"⚠ Could not detect delimiter, using comma")
            
            # Read line by line to find issues
            reader = csv.reader(f, delimiter=delimiter)
            
            for line_num, row in enumerate(reader, 1):
                line_count = line_num
                
                if line_num == 1:
                    expected_columns = len(row)
                    print(f"✓ Expected columns based on header: {expected_columns}")
                    print(f"✓ Header: {row[:5]}{'...' if len(row) > 5 else ''}")
                    continue
                
                current_columns = len(row)
                
                if current_columns != expected_columns:
                    problematic_lines.append({
                        'line': line_num,
                        'expected': expected_columns,
                        'found': current_columns,
                        'content': row[:10]  # First 10 fields for preview
                    })
                    
                    # Focus on the area around line 1609 (the error line)
                    if 1600 <= line_num <= 1620:
                        print(f"❌ Line {line_num}: Expected {expected_columns} columns, found {current_columns}")
                        print(f"   Content preview: {row[:5]}{'...' if len(row) > 5 else ''}")
                
                # Stop after checking a reasonable amount if too many issues
                if len(problematic_lines) > 100:
                    print("⚠ Too many issues found, stopping analysis...")
                    break
                    
    except Exception as e:
        print(f"❌ Error reading file: {e}")
        return
    
    print(f"\n📊 Analysis Results:")
    print(f"   Total lines processed: {line_count}")
    print(f"   Expected columns: {expected_columns}")
    print(f"   Problematic lines found: {len(problematic_lines)}")
    
    if problematic_lines:
        print(f"\n🔧 Suggested fixes:")
        print(f"   1. Use the 'skip bad lines' option in the splitter")
        print(f"   2. Clean the CSV file manually")
        print(f"   3. Use a more flexible CSV parser")
        
        # Show first few problematic lines
        print(f"\n📋 First few problematic lines:")
        for issue in problematic_lines[:10]:
            print(f"   Line {issue['line']}: {issue['expected']} → {issue['found']} columns")
    else:
        print("✅ No column count issues found!")

if __name__ == "__main__":
    diagnose_csv_file()
